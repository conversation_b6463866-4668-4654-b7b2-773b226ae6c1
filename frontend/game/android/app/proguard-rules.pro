## Flutter wrapper
 -keep class io.flutter.app.** { *; }
 -keep class io.flutter.plugin.** { *; }
 -keep class io.flutter.util.** { *; }
 -keep class io.flutter.view.** { *; }
 -keep class io.flutter.** { *; }
 -keep class io.flutter.plugins.** { *; }
 -keep class com.google.firebase.** { *; }
 -dontwarn io.flutter.embedding.**
 -dontwarn proguard.annotation.Keep
 -dontwarn proguard.annotation.KeepClassMembers
 -ignorewarnings
 -keep class com.razorpay.** { *; }
 -keepclassmembers class * {    @android.webkit.JavascriptInterface <methods>;}